{"ecs": {"2014-05-26": {"RetryableAPIs": ["DescribeAccessPoints", "DescribeAccountAttributes", "DescribeAutoSnapshotPolicyEx", "DescribeAvailableResource", "DescribeBandwidthLimitation", "DescribeBandwidthPackages", "DescribeClassicLinkInstances", "DescribeCloudAssistantStatus", "DescribeClusters", "DescribeCommands", "DescribeDedicatedHostAutoRenew", "DescribeDedicatedHosts", "DescribeDedicatedHostTypes", "DescribeDeploymentSets", "DescribeDiskMonitorData", "DescribeDisks", "DescribeDisksFullStatus", "DescribeEipAddresses", "DescribeEipMonitorData", "DescribeEniMonitorData", "DescribeForwardTableEntries", "DescribeHaVips", "DescribeHpcClusters", "DescribeImages", "DescribeImageSharePermission", "DescribeImageSupportInstanceTypes", "DescribeInstanceAttribute", "DescribeInstanceAutoRenewAttribute", "DescribeInstanceHistoryEvents", "DescribeInstanceMonitorData", "DescribeInstancePhysicalAttribute", "DescribeInstanceRamRole", "DescribeInstances", "DescribeInstancesFullStatus", "DescribeInstanceStatus", "DescribeInstanceTopology", "DescribeInstanceTypeFamilies", "DescribeInstanceTypes", "DescribeInstanceVncPasswd", "DescribeInstanceVncUrl", "DescribeInvocationResults", "DescribeInvocations", "DescribeKeyPairs", "DescribeLaunchTemplates", "DescribeLaunchTemplateVersions", "DescribeLimitation", "DescribeNatGateways", "DescribeNetworkInterfacePermissions", "DescribeNetworkInterfaces", "DescribeNewProjectEipMonitorData", "DescribePhysicalConnections", "DescribePrice", "DescribeRecommendInstanceType", "DescribeRegions", "DescribeRenewalPrice", "DescribeResourceByTags", "DescribeResourcesModification", "DescribeRouterInterfaces", "DescribeRouteTables", "DescribeSecurityGroupAttribute", "DescribeSecurityGroupReferences", "DescribeSecurityGroups", "DescribeSnapshotLinks", "DescribeSnapshotMonitorData", "DescribeSnapshotPackage", "DescribeSnapshots", "DescribeSnapshotsUsage", "DescribeSpotPriceHistory", "DescribeTags", "DescribeTaskAttribute", "DescribeTasks", "DescribeUserBusinessBehavior", "DescribeUserData", "DescribeVirtualBorderRouters", "DescribeVirtualBorderRoutersForPhysicalConnection", "DescribeVpcs", "DescribeVRouters", "DescribeVSwitches", "DescribeZones"], "RetryableAPIsWithClientToken": ["CreateDisk", "CreateImage", "CreateInstance", "CreateNetworkInterface", "CreateSnapshot", "ModifyDiskChargeType", "ModifyInstanceChargeType", "ModifyInstanceNetworkSpec", "ModifyInstanceSpec", "ModifyPrepayInstanceSpec", "RenewInstance", "ReplaceSystemDisk", "ResizeDisk", "RunInstances"], "RetryableThrottlingErrors": ["Throttling"], "RetryableNormalErrors": ["InternalError", "UnknownE<PERSON>r", "ServiceUnavailable"]}}}