[project]
name = "alibaba-cloud-ops-mcp-server"
version = "0.8.8"
description = "A MCP server for Alibaba Cloud"
readme = "README.md"
license = {text = "Apache-2.0"}
authors = [
    { name = "<PERSON> Day<PERSON>", email = "<EMAIL>" }
]
requires-python = ">=3.10"
dependencies = [
    "alibabacloud-cms20190101>=3.1.4",
    "alibabacloud-ecs20140526>=6.1.0",
    "alibabacloud-oos20190601>=3.4.1",
    "alibabacloud_oss_v2>=1.1.0",
    "alibabacloud-credentials>=1.0.0",
    "click>=8.1.8",
    "mcp[cli]>=1.9.0",
]

[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"


[tool.setuptools.packages.find]
where = ["src"]

[dependency-groups]
dev = [
    "pytest>=8.4.0",
    "pytest-cov>=6.1.1",
]

[project.scripts]
alibaba-cloud-ops-mcp-server = "alibaba_cloud_ops_mcp_server.server:main"
