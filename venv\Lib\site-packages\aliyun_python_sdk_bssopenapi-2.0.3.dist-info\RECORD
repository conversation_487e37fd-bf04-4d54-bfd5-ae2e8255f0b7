aliyun_python_sdk_bssopenapi-2.0.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
aliyun_python_sdk_bssopenapi-2.0.3.dist-info/LICENSE,sha256=CvyZB4el1cZGT3cy9OYoITxta_Ea2maondKqmi8EcpU,575
aliyun_python_sdk_bssopenapi-2.0.3.dist-info/METADATA,sha256=iU_rS3hn6S4bu5RalSYPk7J6s6VI9FnepNDjl53uyFQ,1490
aliyun_python_sdk_bssopenapi-2.0.3.dist-info/RECORD,,
aliyun_python_sdk_bssopenapi-2.0.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
aliyun_python_sdk_bssopenapi-2.0.3.dist-info/WHEEL,sha256=Z-nyYpwrcSqxfdux5Mbn_DQ525iP7J2DG3JgGvOYyTQ,110
aliyun_python_sdk_bssopenapi-2.0.3.dist-info/top_level.txt,sha256=h97O2RV5thh9sVySFvcAtbsyFnijtIETJbAImpSmbRE,20
aliyunsdkbssopenapi/__init__.py,sha256=U8BEyItnCRffb5NQFYKwkJf1OI4ctsrASQHqqSyNC4k,21
aliyunsdkbssopenapi/__pycache__/__init__.cpython-311.pyc,,
aliyunsdkbssopenapi/__pycache__/endpoint.cpython-311.pyc,,
aliyunsdkbssopenapi/endpoint.py,sha256=QHX-mJjp-PFLYEOK0S-z7wp6NKvdNgB1Efb1reWVkVE,3903
aliyunsdkbssopenapi/request/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
aliyunsdkbssopenapi/request/__pycache__/__init__.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/AddAccountRelationRequest.py,sha256=2h-RIQDUP_zBLxzESgkffqY0rPD4sXDl97Mu8FIwzcE,2819
aliyunsdkbssopenapi/request/v20171214/AllocateCostUnitResourceRequest.py,sha256=d1rjyljL88THSf33ueZOk5n5bWj30Z2CRaHBumUGAtc,3142
aliyunsdkbssopenapi/request/v20171214/ApplyInvoiceRequest.py,sha256=OTYTZeudTLO5vQxyDhpCM_lY723wK5tAz5JDQmBB_Fc,3320
aliyunsdkbssopenapi/request/v20171214/CancelOrderRequest.py,sha256=i4u_8wGh_1O_6mUdLHZVaHKQCrCis2fUzgONJKQHuRQ,1614
aliyunsdkbssopenapi/request/v20171214/ChangeResellerConsumeAmountRequest.py,sha256=AIMrVoNfUgot-NsTKWRdx4mhNuzOydMc5cvWfxBGVXg,2744
aliyunsdkbssopenapi/request/v20171214/ConfirmRelationRequest.py,sha256=VbnB67zYhpbzkobVzH73NgwGIwtpr1UJZBuybStxOhE,2562
aliyunsdkbssopenapi/request/v20171214/ConvertChargeTypeRequest.py,sha256=hwrlyKlwQIWIDLlpEo3RUJapzoI4TL2lsttWGHHkT5c,2434
aliyunsdkbssopenapi/request/v20171214/CreateAgAccountRequest.py,sha256=pE6fXO0OmvY353QCSh8Jh7UWmn8scqfKSJG5Fu-OzHo,3034
aliyunsdkbssopenapi/request/v20171214/CreateCostUnitRequest.py,sha256=NVW_nI5bdw2PYMLyAgiKuzs3pboFZMNnwMZgRVYxVek,2030
aliyunsdkbssopenapi/request/v20171214/CreateInstanceRequest.py,sha256=ZW5oz4Pn6Sayhn5_JSp6SkndtIbl4Q8NUGgMo55MfZU,3515
aliyunsdkbssopenapi/request/v20171214/CreateResellerUserQuotaRequest.py,sha256=JIwFKlgtCMHWDAMPSkz86c6kFGeJfNOvsc3lhwynv9g,1990
aliyunsdkbssopenapi/request/v20171214/CreateResourcePackageRequest.py,sha256=V134ssUtsEDlYj17GNTL-Z3k5uLF1M1RDt1gPXbBJRQ,2657
aliyunsdkbssopenapi/request/v20171214/CreateSavingsPlansInstanceRequest.py,sha256=WcYE25Xhca93WW1yWOHhx9q5K5cMAnrSk_ncbcpghR8,3170
aliyunsdkbssopenapi/request/v20171214/DeleteCostUnitRequest.py,sha256=DlmesG8m9GHZD_1GM30lcVxZ1b7Lx0VpwDMH2garpIA,1616
aliyunsdkbssopenapi/request/v20171214/DescribeInstanceBillRequest.py,sha256=nNqQiTZVeM1-eDLyK4sAog0-z_vcgjBDvXesCq3Nj1M,3879
aliyunsdkbssopenapi/request/v20171214/DescribePricingModuleRequest.py,sha256=0ov0-QrLPMmk9Tf2OOg35EvdlMd-Rr25uGOWTNlpEEE,2082
aliyunsdkbssopenapi/request/v20171214/DescribeResourceCoverageDetailRequest.py,sha256=SLg8QqmWi4RDmxQoHVSDuK1Y502QPgqRP1QxEYfSjr8,2657
aliyunsdkbssopenapi/request/v20171214/DescribeResourceCoverageTotalRequest.py,sha256=Q6lCnCp44AjFFU1oepp3deGBt89hDaQHehGio42taIk,2277
aliyunsdkbssopenapi/request/v20171214/DescribeResourcePackageProductRequest.py,sha256=f3SX3RwCG4NTZgtHxfqtQomMAaoPt3SoqXneZk8ZRyA,1507
aliyunsdkbssopenapi/request/v20171214/DescribeResourceUsageDetailRequest.py,sha256=OkWXhec-PCI1CJAUdL8PLIe36YHhhy0JFAVN8xkpji8,2651
aliyunsdkbssopenapi/request/v20171214/DescribeResourceUsageTotalRequest.py,sha256=oiL7p41zoy2lMs547Zngw66TxEC2zNGTQeHCM8QuGbw,2271
aliyunsdkbssopenapi/request/v20171214/DescribeSavingsPlansCoverageDetailRequest.py,sha256=z9sIu6SAOjufNQtYmyE53LkeUDgVx7faq0_OkcVlEp4,2438
aliyunsdkbssopenapi/request/v20171214/DescribeSavingsPlansCoverageTotalRequest.py,sha256=ARimOIj_nM6O0tTA8J0xVgCZlR1hecO-fXVBlk2nyj4,2082
aliyunsdkbssopenapi/request/v20171214/DescribeSavingsPlansUsageDetailRequest.py,sha256=NNyCNnZhdEQ5esxY8B_h_6ibO7BoKJBUq2fsBjUb0RU,2432
aliyunsdkbssopenapi/request/v20171214/DescribeSavingsPlansUsageTotalRequest.py,sha256=yQtepnQ2fDP0IPPwKCYYi90qanqDPLx7UrRL080IdD0,2076
aliyunsdkbssopenapi/request/v20171214/DescribeSplitItemBillRequest.py,sha256=t9VSw0pwqNJMbxfQ51qJtfpOrWBfPJeiJzD6iSfAqAA,4238
aliyunsdkbssopenapi/request/v20171214/EnableBillGenerationRequest.py,sha256=32cs-B4zUbNdCEsN_4hxDNQFi3PORQ0eL2iQA0ukQ3U,1656
aliyunsdkbssopenapi/request/v20171214/GetCustomerAccountInfoRequest.py,sha256=qQhNakXMKqhfrc0wRRUWZlwAOBwncxblT2lrzykVv-Q,1463
aliyunsdkbssopenapi/request/v20171214/GetCustomerListRequest.py,sha256=mq9-PygOyKjkWcdccjRPE4ENfurkzMAR4j52KEsvp74,1280
aliyunsdkbssopenapi/request/v20171214/GetOrderDetailRequest.py,sha256=Uwd0vlbmSXa6lSeAK9cOUZnhZsncEb_igzA3EIWMS1Y,1620
aliyunsdkbssopenapi/request/v20171214/GetPayAsYouGoPriceRequest.py,sha256=wxKUm8O704dArQ8kDn8rsF-9UHaZIDF2MSrHw9Eza7o,2930
aliyunsdkbssopenapi/request/v20171214/GetResourcePackagePriceRequest.py,sha256=QoqgFyJIw4tUY-FwB_A-9AqE-66gP_zkBS8q7wldINc,3037
aliyunsdkbssopenapi/request/v20171214/GetSubscriptionPriceRequest.py,sha256=M9E-q3HgPZDc6r1U8hvxXgXEblYWBMUficmh_fjmSDA,4141
aliyunsdkbssopenapi/request/v20171214/ModifyAccountRelationRequest.py,sha256=3rNkVhXoDWhEFuZUSn8PqblEFAX2yd5YvvpeJQH4PT4,3058
aliyunsdkbssopenapi/request/v20171214/ModifyCostUnitRequest.py,sha256=0O2H5ZD4LgI65E-qCgF4uUTyUkBz17tkH0s2nYoMyDQ,2021
aliyunsdkbssopenapi/request/v20171214/ModifyInstanceRequest.py,sha256=cRJbrMZuS0qM0lDbXUWMYcyBARl1ZQVO2TAQVS0WQQE,3135
aliyunsdkbssopenapi/request/v20171214/QueryAccountBalanceRequest.py,sha256=R536qBUvjRiddbgP8sH3PgGkGuIITlt0fEmHSotD4jU,1288
aliyunsdkbssopenapi/request/v20171214/QueryAccountBillRequest.py,sha256=iIRufGBcWMTZkZ2AzLenkVrirpseOLQgH1PH4c-RBZ0,3023
aliyunsdkbssopenapi/request/v20171214/QueryAccountTransactionDetailsRequest.py,sha256=NtQe6JjEOFxBvzDCZxpCA3KX50gvb3bg1RCQSHy3FY8,3241
aliyunsdkbssopenapi/request/v20171214/QueryAccountTransactionsRequest.py,sha256=bRCQWWmMjJOjfTbg91f5n2x_5hgK3q2AMTwKv8hx-ZI,2747
aliyunsdkbssopenapi/request/v20171214/QueryAvailableInstancesRequest.py,sha256=KNcrQ0pmy8cn12v1fTVCvqikNVRwOvLpj1V_ZjAXalo,3827
aliyunsdkbssopenapi/request/v20171214/QueryBillOverviewRequest.py,sha256=whbmOTiB35M-nqTbZMt1v7SpDiPL4jlO7jUTPEWNZ_Q,2301
aliyunsdkbssopenapi/request/v20171214/QueryBillRequest.py,sha256=cHK7gcCospsUDskkDZPKjkDABORLPkZ9GQWxI2SrQFo,3459
aliyunsdkbssopenapi/request/v20171214/QueryBillToOSSSubscriptionRequest.py,sha256=FTo7FoHJoZQkaT_vBbDrWa-K63O4oyHHA8Sd3JqEIE8,1302
aliyunsdkbssopenapi/request/v20171214/QueryCashCouponsRequest.py,sha256=TUGd13oji2aBPuq5dWyrAarzrSQ67fmioiv1iuCYfvU,1929
aliyunsdkbssopenapi/request/v20171214/QueryCostUnitRequest.py,sha256=_h991_SpJOTKdgsXcu3DHMNiqAXGgb-iO5TDJEn3Pyw,2006
aliyunsdkbssopenapi/request/v20171214/QueryCostUnitResourceRequest.py,sha256=WMxRn0bw50b39fVBGjjZ_Cpp4sKiGIxU0QNdO5J_Ueg,1986
aliyunsdkbssopenapi/request/v20171214/QueryCustomerAddressListRequest.py,sha256=UJ4tmJkw5NOAu8sRpwd5w0F8n2JUzL1I6yiT_UQwioA,1467
aliyunsdkbssopenapi/request/v20171214/QueryDPUtilizationDetailRequest.py,sha256=yML9YSayjgOOgRYq3gbxUspgKAlJE2k6kXx1Lr5oUBY,3230
aliyunsdkbssopenapi/request/v20171214/QueryEvaluateListRequest.py,sha256=h2irSCLYDKuDDD0KljotO-kjjQcmBxmJXHZV8NmfFB0,3986
aliyunsdkbssopenapi/request/v20171214/QueryFinancialAccountInfoRequest.py,sha256=WIPQRgwoCy-BAEA9KbsnRoG2I2FjmLbeisS_wP_h3RY,1463
aliyunsdkbssopenapi/request/v20171214/QueryInstanceBillRequest.py,sha256=Wwq4v-UbkkCt1_z_GUzSPep_ggYpw3FtNaAtdNqhiQ0,3660
aliyunsdkbssopenapi/request/v20171214/QueryInstanceByTagRequest.py,sha256=YCKjgR939TjIEF-75pBXzrjY-_b8zKQi7b64uVC3xwg,2178
aliyunsdkbssopenapi/request/v20171214/QueryInstanceGaapCostRequest.py,sha256=M5Hpd9G3inTaLmKsmQFaaHdJZOtmgKSJYrkdnQ_UVKM,2472
aliyunsdkbssopenapi/request/v20171214/QueryInvoicingCustomerListRequest.py,sha256=0Xe39V3GObdVKLVJRvoWkFvIKVJL_VvnMGi6loBO4p8,1471
aliyunsdkbssopenapi/request/v20171214/QueryMonthlyBillRequest.py,sha256=x9IVnB3ZpxbxcCbIqKf3b3zsRwJo8mzGbnbR3fYQhN0,1485
aliyunsdkbssopenapi/request/v20171214/QueryMonthlyInstanceConsumptionRequest.py,sha256=b5PzLekJTWMM1Ul3sNfYjZCXOFqcsPblI9HX1c0-TOU,2661
aliyunsdkbssopenapi/request/v20171214/QueryOrdersRequest.py,sha256=1pQk2K50gW7qK_XVPFo0aagZQQCpox5pYB2qyAb3Jxs,3242
aliyunsdkbssopenapi/request/v20171214/QueryPermissionListRequest.py,sha256=lYD6VtqhCtJ5KKq74zP2vCX8NHdgi7DTY9uAjK_lO3c,1475
aliyunsdkbssopenapi/request/v20171214/QueryPrepaidCardsRequest.py,sha256=OIU9LyYuWvJ998qs9ZPCnQu0xeTuSXWEs_q8re46c2A,1931
aliyunsdkbssopenapi/request/v20171214/QueryProductListRequest.py,sha256=j8TCiVblUyPIROkh1KD3IXs57QSqwuM7jpeVMs4IdPg,1861
aliyunsdkbssopenapi/request/v20171214/QueryRIUtilizationDetailRequest.py,sha256=mwWe-mzfaJ5sttk1o9mPe0kLE9E9bs-HEI67UP3JYRI,2878
aliyunsdkbssopenapi/request/v20171214/QueryRedeemRequest.py,sha256=t_ZH9I2sFXtfP4me_eNTn0PMWzPYKyzjKZiBlGJEwOA,2274
aliyunsdkbssopenapi/request/v20171214/QueryRelationListRequest.py,sha256=QdW-6S8yMwSotvPCu_8Zr5ln0VeR_cEgmdoXf6lGHP0,2073
aliyunsdkbssopenapi/request/v20171214/QueryResellerAvailableQuotaRequest.py,sha256=xOHGRksySOpB-DzoR9JThbjrp8-VqOX238RB_470uyI,1658
aliyunsdkbssopenapi/request/v20171214/QueryResourcePackageInstancesRequest.py,sha256=MOJZyhVe4-TtOnYP3alg3l4_zW4yM74W68QXJbuFdkA,2677
aliyunsdkbssopenapi/request/v20171214/QuerySavingsPlansDeductLogRequest.py,sha256=3IiMAbv8WI_k93oEk4Drd0V-y70uBPWU_CsedR89XyI,2577
aliyunsdkbssopenapi/request/v20171214/QuerySavingsPlansInstanceRequest.py,sha256=z5Dj38rFmsyT_atFPVQVcK7PbgueNSbYn4B58dmCecM,2791
aliyunsdkbssopenapi/request/v20171214/QuerySettleBillRequest.py,sha256=dt7UoPAIn8pibUDOgeYklVqugOvu0Xl6iVxG4uYkLwc,3672
aliyunsdkbssopenapi/request/v20171214/QuerySettlementBillRequest.py,sha256=5M6_XgOxeqBqKEYu-PbQZsOqNUf9hwb9AAkSt2WuRd8,3379
aliyunsdkbssopenapi/request/v20171214/QuerySplitItemBillRequest.py,sha256=tayUYQlY-PkEvMJ8vxYphS9-jOl0oGWMQrX5jcLiJhc,2828
aliyunsdkbssopenapi/request/v20171214/QueryUserOmsDataRequest.py,sha256=65j5ozq5KjZRrRuoWFAzH_QRXrpdbNLf6goSIknpirQ,2497
aliyunsdkbssopenapi/request/v20171214/RelieveAccountRelationRequest.py,sha256=QIQLjT7FBrn8qWeeJddgpBD94ig88OUAlh2K3_Qcn_o,2074
aliyunsdkbssopenapi/request/v20171214/RenewInstanceRequest.py,sha256=mBgGKjm1h7jNm_ZkfTUz_D464kgK9h3T_DnCH2CEcsQ,2426
aliyunsdkbssopenapi/request/v20171214/RenewResourcePackageRequest.py,sha256=TN-WQTTq6i9QZNNJacFdI0xewG0Akci55OnUzmxI2KI,2243
aliyunsdkbssopenapi/request/v20171214/SaveUserCreditRequest.py,sha256=zf7xQpB5KfBiXMNFV-zMo_fR0gdRkrPvQMjbsoFmaF4,3077
aliyunsdkbssopenapi/request/v20171214/SetAllExpirationDayRequest.py,sha256=ddZRkss641kqDmGr0p7XIqKbyzF8ylcvF4yAlvfwPno,1672
aliyunsdkbssopenapi/request/v20171214/SetCreditLabelActionRequest.py,sha256=UZNi_oonz2T7Zn0sgYeGe0YrsA0CoEH9HlGATXet5Hg,4804
aliyunsdkbssopenapi/request/v20171214/SetRenewalRequest.py,sha256=1ZABEO_2dyr74ZAtz5kBec8Z-uCxJNQ3w3O-y9-R63A,2910
aliyunsdkbssopenapi/request/v20171214/SetResellerUserAlarmThresholdRequest.py,sha256=urlSFdo5xy1ZWzn-YWU-gqiiVrX9ahcBSy5N7eJffsA,1883
aliyunsdkbssopenapi/request/v20171214/SetResellerUserQuotaRequest.py,sha256=Z32D5bosL2OUiBT2tdSbgv6ty2-WHk9DCRQhYsSC4Bc,1984
aliyunsdkbssopenapi/request/v20171214/SetResellerUserStatusRequest.py,sha256=ibP5lvEX19fBjzRQlfVyC9wLnHp23bAWKysuXIMnnjk,1835
aliyunsdkbssopenapi/request/v20171214/SubscribeBillToOSSRequest.py,sha256=s0xqRqjauLdXhAWDaC0As97b65nyypJWvgh1Xrzafw8,2423
aliyunsdkbssopenapi/request/v20171214/TagResourcesRequest.py,sha256=YA0O2h8i34xLh6nYtYCBbo14Oaql3n1vxvJhATIwlmY,2166
aliyunsdkbssopenapi/request/v20171214/UnsubscribeBillToOSSRequest.py,sha256=8py_o5HZklto7Sa5T4mzDPS2W8_tjn68d7ESLh3W60E,1768
aliyunsdkbssopenapi/request/v20171214/UntagResourcesRequest.py,sha256=ANYgshwLPE6lJXve5FuomUL_yxW6AcdCMnsrS9P3pVg,2144
aliyunsdkbssopenapi/request/v20171214/UpgradeResourcePackageRequest.py,sha256=IOQZdDHqo9Kf4tn_8YBUSfogQ5rz2Rp8AX6hc750Mf4,2072
aliyunsdkbssopenapi/request/v20171214/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
aliyunsdkbssopenapi/request/v20171214/__pycache__/AddAccountRelationRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/AllocateCostUnitResourceRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/ApplyInvoiceRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/CancelOrderRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/ChangeResellerConsumeAmountRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/ConfirmRelationRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/ConvertChargeTypeRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/CreateAgAccountRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/CreateCostUnitRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/CreateInstanceRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/CreateResellerUserQuotaRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/CreateResourcePackageRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/CreateSavingsPlansInstanceRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/DeleteCostUnitRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/DescribeInstanceBillRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/DescribePricingModuleRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/DescribeResourceCoverageDetailRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/DescribeResourceCoverageTotalRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/DescribeResourcePackageProductRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/DescribeResourceUsageDetailRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/DescribeResourceUsageTotalRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/DescribeSavingsPlansCoverageDetailRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/DescribeSavingsPlansCoverageTotalRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/DescribeSavingsPlansUsageDetailRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/DescribeSavingsPlansUsageTotalRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/DescribeSplitItemBillRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/EnableBillGenerationRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/GetCustomerAccountInfoRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/GetCustomerListRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/GetOrderDetailRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/GetPayAsYouGoPriceRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/GetResourcePackagePriceRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/GetSubscriptionPriceRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/ModifyAccountRelationRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/ModifyCostUnitRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/ModifyInstanceRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryAccountBalanceRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryAccountBillRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryAccountTransactionDetailsRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryAccountTransactionsRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryAvailableInstancesRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryBillOverviewRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryBillRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryBillToOSSSubscriptionRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryCashCouponsRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryCostUnitRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryCostUnitResourceRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryCustomerAddressListRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryDPUtilizationDetailRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryEvaluateListRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryFinancialAccountInfoRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryInstanceBillRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryInstanceByTagRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryInstanceGaapCostRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryInvoicingCustomerListRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryMonthlyBillRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryMonthlyInstanceConsumptionRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryOrdersRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryPermissionListRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryPrepaidCardsRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryProductListRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryRIUtilizationDetailRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryRedeemRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryRelationListRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryResellerAvailableQuotaRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryResourcePackageInstancesRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QuerySavingsPlansDeductLogRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QuerySavingsPlansInstanceRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QuerySettleBillRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QuerySettlementBillRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QuerySplitItemBillRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/QueryUserOmsDataRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/RelieveAccountRelationRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/RenewInstanceRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/RenewResourcePackageRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/SaveUserCreditRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/SetAllExpirationDayRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/SetCreditLabelActionRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/SetRenewalRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/SetResellerUserAlarmThresholdRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/SetResellerUserQuotaRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/SetResellerUserStatusRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/SubscribeBillToOSSRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/TagResourcesRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/UnsubscribeBillToOSSRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/UntagResourcesRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/UpgradeResourcePackageRequest.cpython-311.pyc,,
aliyunsdkbssopenapi/request/v20171214/__pycache__/__init__.cpython-311.pyc,,
