#!/usr/bin/env python3
"""
检查BSS OpenAPI中可用的API
"""
import sys
sys.path.append('src')

from alibaba_cloud_ops_mcp_server.alibabacloud.api_meta_client import ApiMetaClient

def check_bss_apis():
    """检查BSS OpenAPI中可用的API"""
    try:
        print("Checking available BSS OpenAPI...")
        
        # 获取BSS服务版本
        version = ApiMetaClient.get_service_version('bssopenapi')
        print(f"BSS OpenAPI version: {version}")
        
        # 获取BSS服务中的所有API
        apis = ApiMetaClient.get_apis_in_service('bssopenapi', version)
        print(f"\nFound {len(apis)} APIs in BSS OpenAPI:")
        
        # 查找账单相关的API
        bill_apis = []
        for api in sorted(apis):
            if 'bill' in api.lower() or 'account' in api.lower():
                bill_apis.append(api)
                print(f"  📋 {api}")
            else:
                print(f"     {api}")
        
        print(f"\n🎯 Found {len(bill_apis)} bill-related APIs:")
        for api in bill_apis:
            try:
                api_meta, _ = ApiMetaClient.get_api_meta('bssopenapi', api)
                summary = api_meta.get('summary', 'No description')
                print(f"  - {api}: {summary}")
            except Exception as e:
                print(f"  - {api}: Error getting description - {e}")
        
        return bill_apis
        
    except Exception as e:
        print(f"❌ Error checking BSS APIs: {e}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    check_bss_apis()
