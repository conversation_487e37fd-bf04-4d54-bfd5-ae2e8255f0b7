#!/usr/bin/env python3
"""
阿里云账单查询示例
演示如何使用BSS OpenAPI查询账单信息
"""
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from mcp.server.fastmcp import FastMCP
from alibaba_cloud_ops_mcp_server.tools import api_tools

def create_billing_server():
    """创建包含账单查询功能的MCP服务器"""
    mcp = FastMCP("Billing Query Server")
    
    # 配置账单相关的API
    billing_config = {
        'bssopenapi': [
            'QueryBill',              # 详细账单查询
            'QueryAccountBill',       # 账号账单查询（汇总）
            'DescribeInstanceBill',   # 实例账单查询
            'QueryBillOverview',      # 账单总览
            'DescribeSplitItemBill'   # 分账账单查询
        ]
    }
    
    # 创建API工具
    api_tools.create_api_tools(mcp, billing_config)
    
    return mcp

def main():
    """主函数"""
    print("🏦 阿里云账单查询服务器")
    print("=" * 50)
    
    # 创建服务器
    server = create_billing_server()
    
    print("✅ 账单查询服务器创建成功！")
    print("\n📋 可用的账单查询工具：")
    print("  - BSSOPENAPI_QueryBill: 查询详细账单")
    print("  - BSSOPENAPI_QueryAccountBill: 查询账号汇总账单")
    print("  - BSSOPENAPI_DescribeInstanceBill: 查询实例账单")
    print("  - BSSOPENAPI_QueryBillOverview: 查询账单总览")
    print("  - BSSOPENAPI_DescribeSplitItemBill: 查询分账账单")
    
    print("\n💡 使用示例：")
    print("  # 查询上个月的账单总览")
    last_month = datetime.now().replace(day=1) - timedelta(days=1)
    billing_cycle = last_month.strftime('%Y-%m')
    print(f"  BSSOPENAPI_QueryBillOverview(BillingCycle='{billing_cycle}')")
    
    print(f"\n  # 查询上个月的账号账单（分页）")
    print(f"  BSSOPENAPI_QueryAccountBill(BillingCycle='{billing_cycle}', PageNum=1, PageSize=20)")
    
    print(f"\n  # 查询上个月的详细账单")
    print(f"  BSSOPENAPI_QueryBill(BillingCycle='{billing_cycle}', Type='PayAsYouGoBill')")
    
    print("\n🚀 启动服务器...")
    print("使用 Ctrl+C 停止服务器")
    
    try:
        # 启动服务器（默认使用stdio传输）
        server.run(transport='stdio')
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")

if __name__ == "__main__":
    main()
