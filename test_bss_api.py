#!/usr/bin/env python3
"""
测试BSS API功能
"""
import sys
import os
sys.path.append('src')

from alibaba_cloud_ops_mcp_server.tools.api_tools import _tools_api_call
from alibaba_cloud_ops_mcp_server.alibabacloud.api_meta_client import ApiMetaClient

def test_bss_api():
    """测试BSS API是否可用"""
    try:
        # 测试获取BSS服务版本
        print("Testing BSS API availability...")
        
        # 获取BSS服务版本
        version = ApiMetaClient.get_service_version('bssopenapi')
        print(f"BSS OpenAPI version: {version}")
        
        # 获取QueryBill API元数据
        api_meta, _ = ApiMetaClient.get_api_meta('bssopenapi', 'QueryBill')
        print(f"QueryBill API found: {api_meta.get('summary', 'No summary')}")
        
        # 测试QueryAccountBill API元数据
        api_meta2, _ = ApiMetaClient.get_api_meta('bssopenapi', 'QueryAccountBill')
        print(f"QueryAccountBill API found: {api_meta2.get('summary', 'No summary')}")
        
        print("✅ BSS API configuration is working!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing BSS API: {e}")
        return False

if __name__ == "__main__":
    test_bss_api()
