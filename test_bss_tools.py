#!/usr/bin/env python3
"""
测试BSS工具功能
"""
import sys
import os
from datetime import datetime, timedelta
sys.path.append('src')

from mcp.server.fastmcp import FastMCP
from alibaba_cloud_ops_mcp_server.tools.api_tools import create_api_tools

def test_bss_tools():
    """测试BSS工具是否正确创建"""
    try:
        print("Testing BSS tools creation...")
        
        # 创建MCP服务器
        mcp = FastMCP("test-server")
        
        # 只测试BSS配置
        bss_config = {
            'bssopenapi': [
                'QueryBill',
                'QueryAccountBill',
                'QueryBillOverview',
                'QueryMonthlyBill'
            ]
        }
        
        # 创建API工具
        create_api_tools(mcp, bss_config)
        
        # 检查工具是否创建成功
        tools = mcp.list_tools()
        print(f"Created {len(tools)} BSS tools:")
        
        for tool in tools:
            print(f"  - {tool.name}: {tool.description}")
        
        # 验证预期的工具都存在
        expected_tools = [
            'BSSOPENAPI_QueryBill',
            'BSSOPENAPI_QueryAccountBill', 
            'BSSOPENAPI_QueryBillOverview',
            'BSSOPENAPI_QueryMonthlyBill'
        ]
        
        created_tool_names = [tool.name for tool in tools]
        
        for expected in expected_tools:
            if expected in created_tool_names:
                print(f"✅ {expected} created successfully")
            else:
                print(f"❌ {expected} not found")
        
        print("✅ BSS tools creation test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing BSS tools: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_bss_tools()
